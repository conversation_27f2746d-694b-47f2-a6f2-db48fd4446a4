export declare function createClipboardItem(window: Window & typeof globalThis, ...blobs: Array<Blob | string>): ClipboardItem;
type ClipboardStubControlInstance = {
    resetClipboardStub: () => void;
    detachClipboardStub: () => void;
};
export declare function attachClipboardStubToView(window: Window & typeof globalThis): ClipboardStubControlInstance;
export declare function resetClipboardStubOnView(window: Window & typeof globalThis): void;
export declare function detachClipboardStubFromView(window: Window & typeof globalThis): void;
export declare function readDataTransferFromClipboard(document: Document): Promise<DataTransfer>;
export declare function writeDataTransferToClipboard(document: Document, clipboardData: DataTransfer): Promise<void>;
export {};
