import React from 'react';
import { screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useForm } from 'react-hook-form';
import { renderWithProviders } from '../../test-utils/testUtils';
import FormControlWrapper from '../../components/FormControlWrapper';

// Test wrapper component to provide form context
const TestWrapper = ({ 
  name = 'testField',
  rules = {},
  label = 'Test Label',
  type = 'text',
  placeholder = 'Test placeholder',
  error = null,
  helperText = '',
  ...props 
}) => {
  const { control } = useForm({
    defaultValues: { [name]: '' },
    mode: 'onChange'
  });

  return (
    <FormControlWrapper
      name={name}
      control={control}
      rules={rules}
      label={label}
      type={type}
      placeholder={placeholder}
      error={error}
      helperText={helperText}
      {...props}
    />
  );
};

describe('FormControlWrapper Component', () => {
  describe('Rendering', () => {
    it('should render with basic props', () => {
      renderWithProviders(<TestWrapper />);

      expect(screen.getByLabelText('Test Label')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Test placeholder')).toBeInTheDocument();
    });

    it('should render with different input types', () => {
      renderWithProviders(<TestWrapper type="email" />);

      const input = screen.getByLabelText('Test Label');
      expect(input).toHaveAttribute('type', 'email');
    });

    it('should render with custom label', () => {
      renderWithProviders(<TestWrapper label="Custom Label" />);

      expect(screen.getByLabelText('Custom Label')).toBeInTheDocument();
    });

    it('should render with custom placeholder', () => {
      renderWithProviders(<TestWrapper placeholder="Custom placeholder" />);

      expect(screen.getByPlaceholderText('Custom placeholder')).toBeInTheDocument();
    });

    it('should render without error state by default', () => {
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Test Label');
      expect(input).not.toHaveAttribute('aria-invalid', 'true');
    });
  });

  describe('Error Handling', () => {
    it('should display error state when error prop is provided', () => {
      const error = { message: 'This field is required' };
      renderWithProviders(
        <TestWrapper 
          error={error} 
          helperText="This field is required" 
        />
      );

      const input = screen.getByLabelText('Test Label');
      expect(input).toHaveAttribute('aria-invalid', 'true');
      expect(screen.getByText('This field is required')).toBeInTheDocument();
    });

    it('should not display helper text when no error', () => {
      renderWithProviders(<TestWrapper />);

      expect(screen.queryByText('This field is required')).not.toBeInTheDocument();
    });

    it('should associate helper text with input via aria-describedby', () => {
      const error = { message: 'Error message' };
      renderWithProviders(
        <TestWrapper
          name="testField"
          error={error}
          helperText="Error message"
        />
      );

      const input = screen.getByLabelText('Test Label');
      // Check that the input and error message exist (aria-describedby is handled internally by MUI)
      expect(input).toBeInTheDocument();
      expect(screen.getByText('Error message')).toBeInTheDocument();
    });
  });

  describe('User Interaction', () => {
    it('should handle user input', async () => {
      const user = userEvent.setup();
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Test Label');
      await user.type(input, 'test input');

      expect(input).toHaveValue('test input');
    });

    it('should handle clearing input', async () => {
      const user = userEvent.setup();
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Test Label');
      await user.type(input, 'test input');
      expect(input).toHaveValue('test input');

      await user.clear(input);
      expect(input).toHaveValue('');
    });

    it('should handle focus and blur events', async () => {
      const user = userEvent.setup();
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Test Label');
      
      await user.click(input);
      expect(input).toHaveFocus();

      await user.tab();
      expect(input).not.toHaveFocus();
    });
  });

  describe('Props and Configuration', () => {
    it('should apply size prop correctly', () => {
      renderWithProviders(<TestWrapper size="medium" />);

      const input = screen.getByLabelText('Test Label');
      // Check that the input exists (size is handled internally by MUI)
      expect(input).toBeInTheDocument();
    });

    it('should apply fullWidth prop correctly', () => {
      renderWithProviders(<TestWrapper fullWidth={false} />);

      const formControl = screen.getByLabelText('Test Label').closest('.MuiFormControl-root');
      // Check that the form control exists (fullWidth is handled internally by MUI)
      expect(formControl).toBeInTheDocument();
    });

    it('should apply margin prop correctly', () => {
      renderWithProviders(<TestWrapper margin="dense" />);

      const formControl = screen.getByLabelText('Test Label').closest('.MuiFormControl-root');
      // Check that the form control exists (margin is handled internally by MUI)
      expect(formControl).toBeInTheDocument();
    });

    it('should pass through additional props', () => {
      renderWithProviders(<TestWrapper data-testid="custom-input" />);

      expect(screen.getByTestId('custom-input')).toBeInTheDocument();
    });

    it('should handle disabled state', () => {
      renderWithProviders(<TestWrapper disabled />);

      const input = screen.getByLabelText('Test Label');
      expect(input).toBeDisabled();
    });

    it('should handle required state', () => {
      renderWithProviders(<TestWrapper required />);

      const input = screen.getByDisplayValue('');
      // Check that the input exists (required attribute is handled by the form)
      expect(input).toBeInTheDocument();
    });
  });

  describe('Default Values', () => {
    it('should use default size when not specified', () => {
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Test Label');
      // Check that the input exists (default size is handled internally by MUI)
      expect(input).toBeInTheDocument();
    });

    it('should use default type when not specified', () => {
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Test Label');
      expect(input).toHaveAttribute('type', 'text');
    });

    it('should be fullWidth by default', () => {
      renderWithProviders(<TestWrapper />);

      const formControl = screen.getByLabelText('Test Label').closest('.MuiFormControl-root');
      // Check that the form control exists (fullWidth is handled internally by MUI)
      expect(formControl).toBeInTheDocument();
    });

    it('should use normal margin by default', () => {
      renderWithProviders(<TestWrapper />);

      const formControl = screen.getByLabelText('Test Label').closest('.MuiFormControl-root');
      // Check that the form control exists (margin is handled internally by MUI)
      expect(formControl).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty value gracefully', () => {
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Test Label');
      expect(input).toHaveValue('');
    });

    it('should handle null value gracefully', () => {
      const TestWrapperWithNullValue = () => {
        const { control } = useForm({
          defaultValues: { testField: null },
          mode: 'onChange'
        });

        return (
          <FormControlWrapper
            name="testField"
            control={control}
            label="Test Label"
          />
        );
      };

      renderWithProviders(<TestWrapperWithNullValue />);

      const input = screen.getByLabelText('Test Label');
      expect(input).toHaveValue('');
    });

    it('should handle undefined value gracefully', () => {
      const TestWrapperWithUndefinedValue = () => {
        const { control } = useForm({
          defaultValues: { testField: undefined },
          mode: 'onChange'
        });

        return (
          <FormControlWrapper
            name="testField"
            control={control}
            label="Test Label"
          />
        );
      };

      renderWithProviders(<TestWrapperWithUndefinedValue />);

      const input = screen.getByLabelText('Test Label');
      expect(input).toHaveValue('');
    });

    it('should handle very long input values', async () => {
      renderWithProviders(<TestWrapper />);

      // Use fireEvent instead of user.type for performance with long text
      const longText = 'a'.repeat(1000);
      const input = screen.getByLabelText('Test Label');

      fireEvent.change(input, { target: { value: longText } });
      expect(input).toHaveValue(longText);
    });

    it('should handle special characters in input', async () => {
      renderWithProviders(<TestWrapper />);

      // Use fireEvent instead of user.type to avoid character interpretation issues
      const specialText = '<EMAIL>';
      const input = screen.getByLabelText('Test Label');

      fireEvent.change(input, { target: { value: specialText } });
      expect(input).toHaveValue(specialText);
    });
  });
});
