import React from 'react';
import { screen } from '@testing-library/react';
import { renderWithProviders } from '../../test-utils/testUtils';
import PrivateRoute from '../../components/PrivateRoute';
import authService from '../../api/authService';

// Mock the authService
jest.mock('../../api/authService');
const mockedAuthService = authService;

// Mock react-router-dom Navigate component
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  Navigate: ({ to }) => {
    mockNavigate(to);
    return <div data-testid="navigate-component">Redirecting to {to}</div>;
  },
}));

describe('PrivateRoute', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockNavigate.mockClear();
  });

  const TestComponent = () => (
    <div data-testid="protected-content">
      This is protected content
    </div>
  );

  describe('when user is authenticated', () => {
    beforeEach(() => {
      mockedAuthService.isAuthenticated.mockReturnValue(true);
    });

    it('should render children when user is authenticated', () => {
      renderWithProviders(
        <PrivateRoute>
          <TestComponent />
        </PrivateRoute>
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();
      expect(screen.getByText('This is protected content')).toBeInTheDocument();
      expect(screen.queryByTestId('navigate-component')).not.toBeInTheDocument();
    });

    it('should call authService.isAuthenticated', () => {
      renderWithProviders(
        <PrivateRoute>
          <TestComponent />
        </PrivateRoute>
      );

      expect(mockedAuthService.isAuthenticated).toHaveBeenCalledTimes(1);
    });

    it('should render multiple children when authenticated', () => {
      renderWithProviders(
        <PrivateRoute>
          <div data-testid="child-1">Child 1</div>
          <div data-testid="child-2">Child 2</div>
        </PrivateRoute>
      );

      expect(screen.getByTestId('child-1')).toBeInTheDocument();
      expect(screen.getByTestId('child-2')).toBeInTheDocument();
    });
  });

  describe('when user is not authenticated', () => {
    beforeEach(() => {
      mockedAuthService.isAuthenticated.mockReturnValue(false);
    });

    it('should redirect to login when user is not authenticated', () => {
      renderWithProviders(
        <PrivateRoute>
          <TestComponent />
        </PrivateRoute>
      );

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
      expect(screen.getByTestId('navigate-component')).toBeInTheDocument();
      expect(screen.getByText('Redirecting to /login')).toBeInTheDocument();
      expect(mockNavigate).toHaveBeenCalledWith('/login');
    });

    it('should not render children when not authenticated', () => {
      renderWithProviders(
        <PrivateRoute>
          <TestComponent />
        </PrivateRoute>
      );

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
      expect(screen.queryByText('This is protected content')).not.toBeInTheDocument();
    });

    it('should call authService.isAuthenticated', () => {
      renderWithProviders(
        <PrivateRoute>
          <TestComponent />
        </PrivateRoute>
      );

      expect(mockedAuthService.isAuthenticated).toHaveBeenCalledTimes(1);
    });
  });

  describe('edge cases', () => {
    it('should handle null children gracefully when authenticated', () => {
      mockedAuthService.isAuthenticated.mockReturnValue(true);

      renderWithProviders(
        <PrivateRoute>
          {null}
        </PrivateRoute>
      );

      expect(mockedAuthService.isAuthenticated).toHaveBeenCalledTimes(1);
      expect(screen.queryByTestId('navigate-component')).not.toBeInTheDocument();
    });

    it('should handle undefined children gracefully when authenticated', () => {
      mockedAuthService.isAuthenticated.mockReturnValue(true);

      renderWithProviders(
        <PrivateRoute>
          {undefined}
        </PrivateRoute>
      );

      expect(mockedAuthService.isAuthenticated).toHaveBeenCalledTimes(1);
      expect(screen.queryByTestId('navigate-component')).not.toBeInTheDocument();
    });

    it('should handle empty children when authenticated', () => {
      mockedAuthService.isAuthenticated.mockReturnValue(true);

      renderWithProviders(
        <PrivateRoute />
      );

      expect(mockedAuthService.isAuthenticated).toHaveBeenCalledTimes(1);
      expect(screen.queryByTestId('navigate-component')).not.toBeInTheDocument();
    });

    it('should handle authService throwing an error', () => {
      mockedAuthService.isAuthenticated.mockImplementation(() => {
        throw new Error('Auth service error');
      });

      // Should not crash and should redirect to login
      expect(() => {
        renderWithProviders(
          <PrivateRoute>
            <TestComponent />
          </PrivateRoute>
        );
      }).toThrow('Auth service error');
    });

    it('should handle authService returning non-boolean values', () => {
      // Test truthy values
      mockedAuthService.isAuthenticated.mockReturnValue('token');
      
      renderWithProviders(
        <PrivateRoute>
          <TestComponent />
        </PrivateRoute>
      );

      expect(screen.getByTestId('protected-content')).toBeInTheDocument();

      // Test falsy values
      mockedAuthService.isAuthenticated.mockReturnValue('');
      
      renderWithProviders(
        <PrivateRoute>
          <TestComponent />
        </PrivateRoute>
      );

      expect(screen.getByTestId('navigate-component')).toBeInTheDocument();
    });
  });

  describe('integration scenarios', () => {
    it('should work with complex nested components when authenticated', () => {
      mockedAuthService.isAuthenticated.mockReturnValue(true);

      const ComplexComponent = () => (
        <div data-testid="complex-component">
          <h1>Dashboard</h1>
          <div>
            <button>Action 1</button>
            <button>Action 2</button>
          </div>
        </div>
      );

      renderWithProviders(
        <PrivateRoute>
          <ComplexComponent />
        </PrivateRoute>
      );

      expect(screen.getByTestId('complex-component')).toBeInTheDocument();
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Action 1')).toBeInTheDocument();
      expect(screen.getByText('Action 2')).toBeInTheDocument();
    });

    it('should maintain component state when re-rendered with same auth status', () => {
      mockedAuthService.isAuthenticated.mockReturnValue(true);

      const StatefulComponent = () => {
        const [count, setCount] = React.useState(0);
        return (
          <div data-testid="stateful-component">
            <span data-testid="count">{count}</span>
            <button onClick={() => setCount(c => c + 1)}>Increment</button>
          </div>
        );
      };

      const { rerender } = renderWithProviders(
        <PrivateRoute>
          <StatefulComponent />
        </PrivateRoute>
      );

      expect(screen.getByTestId('count')).toHaveTextContent('0');

      // Re-render with same auth status
      rerender(
        <PrivateRoute>
          <StatefulComponent />
        </PrivateRoute>
      );

      expect(screen.getByTestId('count')).toHaveTextContent('0');
      expect(mockedAuthService.isAuthenticated).toHaveBeenCalledTimes(2);
    });
  });
});
