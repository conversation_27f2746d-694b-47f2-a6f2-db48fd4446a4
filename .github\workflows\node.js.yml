# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: Node.js CI

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:
  build:

    runs-on: ${{matrix.os}}

    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macOs-latest]
        node-version: [20.x, 22.x]
      fail-fast: false

    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    - run: npm ci # install dependencies
    - run: npm run build --if-present # build the project
    - run: npm test # run tests
