import { click, dblClick, tripleClick } from '../convenience/click.js';
import { hover, unhover } from '../convenience/hover.js';
import { tab } from '../convenience/tab.js';
import { keyboard } from '../keyboard/index.js';
import { copy } from '../clipboard/copy.js';
import { cut } from '../clipboard/cut.js';
import { paste } from '../clipboard/paste.js';
import { pointer } from '../pointer/index.js';
import { clear } from '../utility/clear.js';
import { deselectOptions, selectOptions } from '../utility/selectOptions.js';
import { type } from '../utility/type.js';
import { upload } from '../utility/upload.js';

const userEventApi = {
    click,
    dblClick,
    tripleClick,
    hover,
    unhover,
    tab,
    keyboard,
    copy,
    cut,
    paste,
    pointer,
    clear,
    deselectOptions,
    selectOptions,
    type,
    upload
};

export { userEventApi };
